package fm.lizhi.ocean.wave.assistant.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.map.MapUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoom;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.param.GetLiveRoomParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.api.live.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.assistant.core.config.AssistantConfig;
import fm.lizhi.ocean.wave.assistant.core.config.AssistantMsgCodes;
import fm.lizhi.ocean.wave.assistant.core.constant.CheckInOperateHostEnum;
import fm.lizhi.ocean.wave.assistant.core.convert.CheckInConvert;
import fm.lizhi.ocean.wave.assistant.core.datastore.entity.WaveCheckInRecord;
import fm.lizhi.ocean.wave.assistant.core.datastore.entity.WaveCheckInSchedule;
import fm.lizhi.ocean.wave.assistant.core.datastore.redis.CheckInRedisDao;
import fm.lizhi.ocean.wave.assistant.core.kafka.bean.SimpleGiftMsg;
import fm.lizhi.ocean.wave.assistant.core.model.bean.*;
import fm.lizhi.ocean.wave.assistant.core.model.param.CheckInOperateHostParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.CheckInOperateParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetAllMicGiftNotAllocationParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetCheckInDetailParam;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInAllMicNotAllocationResult;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInConfigResult;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInDetailsResult;
import fm.lizhi.ocean.wave.assistant.core.model.vo.CheckInAllMicNotAllocationVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.CheckInDetailVO;
import fm.lizhi.ocean.wave.assistant.core.model.vo.PageCheckInAllMicRecordVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.PageLightGiftRecordVo;
import fm.lizhi.ocean.wave.assistant.core.remote.service.CheckInServiceRemote;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.constant.TimeConstant;
import fm.lizhi.ocean.wave.common.exception.TryLockException;
import fm.lizhi.ocean.wave.common.model.bean.PageBean;
import fm.lizhi.ocean.wave.common.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.common.vo.PageVo;
import fm.lizhi.ocean.wave.platform.api.platform.constant.*;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestIncrIncomeAndTask;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveCheckInService;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.FamilyInfoBean;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 排班打卡功能
 */
@Slf4j
@Component
public class CheckInManager {

    @Autowired
    private CheckInRedisDao checkInRedisDao;

    @Autowired
    private AssistantConfig assistantConfig;

    @Autowired
    private CheckInRecordManager checkInRecordManager;

    @Autowired
    private CheckInScheduleManager checkInScheduleManager;

    @Autowired
    private LiveService liveService;

    @Autowired
    private UserService userService;

    @Autowired
    private LiveRoomService liveRoomService;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private FamilyService familyService;

    @Autowired
    private CheckInServiceRemote checkInServiceRemote;



    /**
     * 线程池
     */
    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors(
            "checkInManager.syncScheduleHost", 3, 6, new ArrayBlockingQueue<Runnable>(100));

    /**
     * 获取考核魅力值
     *
     * @param roomId 房间ID
     * @return 考核魅力值
     */
    public int getExamCharm(long roomId) {
        int appId = ContextUtils.getContext().getHeader().getAppId();
        int examCharm = checkInRedisDao.getExamCharm(appId, roomId);
        if (examCharm < 0) {
            //从mysql查询
            Optional<Integer> curScheduleExamCharm = checkInScheduleManager.getLatestScheduleExamCharm(roomId);
            //查询不到从mysql查询，查询成功就设置到缓存中
            if (curScheduleExamCharm.isPresent()) {
                examCharm = curScheduleExamCharm.get();
                checkInRedisDao.updateExamCharm(appId, roomId, examCharm);
                return examCharm;
            }
            return assistantConfig.getCheckInDefaultExamCharm();
        }

        return examCharm;
    }


    /**
     * 统计收入信息
     *
     * @param giftMsg 礼物信息
     * @return true: 成功，false：失败
     */
    public void statIncomeInfo(SimpleGiftMsg giftMsg) {
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        // 查询是否已经消费过这条数据了
        if (checkInRedisDao.isIdempotent(appId, giftMsg.getTransactionId())) {
            //已经消费过了，打印一行日志，结束
            log.info("giftMsg has been consumed, appId:{}, transactionId:{}", appId, giftMsg.getTransactionId());
            return;
        }

        //将魅力值取绝对值
        giftMsg.setValue(Math.abs(giftMsg.getValue()));

        //查询房间ID和家族ID
        Optional<Pair<Long, Long>> liveRoomRes = getRoomIdAndFamilyId(giftMsg.getLiveId(), giftMsg.getRecUserId());
        if (!liveRoomRes.isPresent()) {
            log.warn("statIncomeInfo.getRoomIdAndFamilyId fail, appId:{}, transactionId:{}, liveId:{}", appId, giftMsg.getTransactionId(), giftMsg.getLiveId());
            throw new RuntimeException("statIncomeInfo.getRoomIdAndFamilyId fail");
        }

        //直播间不存在就算消费成功
        Pair<Long, Long> roomAndFamilyIdRes = liveRoomRes.get();
        Long roomId = roomAndFamilyIdRes.getLeft();
        if (roomId < 0) {
            return;
        }

        //累加数据，需要加个分布式锁
        try (RedisLock lock = checkInRedisDao.getAddCharmLock(roomId)) {
            if (!lock.tryLock()) {
                //如果没有拿到锁，则等待下一次
                log.warn("statIncomeInfo.getAddCharmLock fail, appId:{}, transactionId:{}, liveId:{}", appId, giftMsg.getTransactionId(), giftMsg.getLiveId());
                throw new TryLockException("statIncomeInfo.getAddCharmLock fail");
            }

            // 查询是否已经初始化过档期记录和打卡记录
            Long scheduleId = checkInScheduleManager.initSchedule(appId, roomId,
                    roomAndFamilyIdRes.getRight(), giftMsg.getCreateTime(), giftMsg.getRecUserId(), true);
            if (scheduleId < 0) {
                log.warn("statIncomeInfo.initSchedule fail, appId:{}, transactionId:{}, liveId:{}", appId, giftMsg.getTransactionId(), giftMsg.getGiftId());
                throw new RuntimeException("statIncomeInfo.initSchedule fail");
            }

            // 初始化打卡记录
            boolean res = checkInRecordManager.initCheckInRecord(appId, scheduleId, giftMsg.getRecTargetUserId(), roomId);
            if (!res) {
                log.warn("statIncomeInfo.initCheckInRecord fail, appId:{}, userId:{}, scheduleId:{}", appId, giftMsg.getRecTargetUserId(), scheduleId);
                throw new RuntimeException("statIncomeInfo.initCheckInRecord fail");
            }

            RequestIncrIncomeAndTask request = new RequestIncrIncomeAndTask();
            request.setScheduleId(scheduleId);
            request.setRoomId(roomId);
            request.setFamilyId(roomAndFamilyIdRes.getRight());
            request.setRecTargetUserId(giftMsg.getRecTargetUserId());
            request.setTransactionId(giftMsg.getTransactionId());
            request.setGiftValue(giftMsg.getValue());
            request.setLitchiAmount(giftMsg.getLitchiAmount());
            request.setAppId(appId);
            request.setSendUserId(giftMsg.getSendUserId());
            request.setLiveId(giftMsg.getLiveId());
            request.setUnitPrice(giftMsg.getUnitPrice());
            request.setGiftAmount(giftMsg.getGiftAmount());
            request.setGiftId(giftMsg.getGiftId());
            request.setNjId(giftMsg.getRecUserId());
            request.setGiftName(giftMsg.getGiftName());
            request.setGiftBatchId(giftMsg.getGiftBatchId());
            request.setGiftSource(giftMsg.getGiftSource());

            Result<Void> result = checkInServiceRemote.increaseIncomeAndTask(request);
            if (RpcResult.isFail(result)){
                log.error("statIncomeInfo.increaseIncomeAndTask fail, appId:{}, userId:{}, scheduleId:{}", appId, giftMsg.getRecTargetUserId(), scheduleId);
                // 出现异常，取消幂等
                checkInRedisDao.cancelIdempotent(request.getAppId(), request.getTransactionId());
                throw new RuntimeException("statIncomeInfo.increaseIncomeAndTask fail");
            }else {
                // 设置幂等
                checkInRedisDao.setIdempotent(request.getAppId(), request.getTransactionId());
                // 删除缓存
                checkInRedisDao.delCheckInDetailsCache(scheduleId);
            }

        }
    }


    /**
     * 初始化档期
     *
     * @param appId
     * @param scheduleStartTime
     * @param njId
     * @param initHostByConfig
     * @return -1 代表初始化档期失败
     */
    public Long initSchedule(Integer appId, Long roomId, long scheduleStartTime, Long njId, boolean initHostByConfig) {
        long familyId = 0;
        //查询房间ID和家族ID
        Result<FamilyInfoBean> familyRes = familyService.getUserInFamily(njId);
        //查询失败
        if (familyRes.rCode() == FamilyService.GET_USER_IN_FAMILY_PARAM_FAIL) {
            log.error("getRoomIdAndFamilyId.getUserInFamily fail, njId:{}", njId);
        }

        if (familyRes.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            familyId = familyRes.target().getId();
        }
        // 查询是否已经初始化过档期记录和打卡记录
        return checkInScheduleManager.initSchedule(appId, roomId, familyId, scheduleStartTime, njId, initHostByConfig);
    }


    /**
     * 此方法提供给 checkInDetail 或 checkHost 使用，会在整点发起大量请求， 所以这里要加锁初始化
     * @param initHostByConfig 从 web 站的主持人配置中初始化主持人
     */
    private WaveCheckInSchedule getScheduleIfNotExistInit(Long roomId, Long scheduleId, Long scheduleStartTime, boolean initHostByConfig) {
        WaveCheckInSchedule schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(roomId, scheduleId, scheduleStartTime);

        // 不是当前档期或者上一个档期, 直接返回，不管是不是null.
        if (!checkInScheduleManager.isCurrentOrPreSchedule(new Date(scheduleStartTime))){
            return schedule;
        }

        // 当前档期，档期为空，需要初始化
        if (schedule == null) {

            try (RedisLock lock = checkInRedisDao.getInitScheduleLock(roomId, scheduleStartTime)) {
                if (!lock.lock()) {
                    //如果没有拿到锁，则等待下一次
                    log.warn("getCheckInDetailSchedule lock fail, roomId:{}, scheduleStartTime:{}", roomId, scheduleStartTime);
                    return null;
                }

                // 再查一次，等待拿到锁的话会有数据
                schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(roomId, scheduleId, scheduleStartTime);
                if (schedule != null){
                    return schedule;
                }

                log.info("getCheckInDetailSchedule schedule is null. init schedule, roomId:{}, scheduleId:{}, scheduleStartTime:{}", roomId, scheduleId, scheduleStartTime);
                Integer appId = ContextUtils.getContext().getHeader().getAppId();
                Long njId = getNjIdByRoomId(roomId);
                // 初始化档期
                scheduleId = initSchedule(appId, roomId, scheduleStartTime, njId, initHostByConfig);
                if (scheduleId < 0) {
                    return null;
                }
                return checkInScheduleManager.getScheduleById(scheduleId);
            } catch (Exception e) {
                log.error("getCheckInDetailSchedule fail, roomId:{}, scheduleStartTime:{}", roomId, scheduleStartTime, e);
            }
        }
        return schedule;
    }


    /**
     * 获取打卡详情
     *
     * @param param 参数
     * @return 结果
     */
    public ResultVO<CheckInDetailVO> getCheckInDetail(GetCheckInDetailParam param) {
        if (param.getRoomId() < 0 || (param.getScheduleId() == null && param.getScheduleStartTime() == null)) {
            return ResultVO.failure(AssistantMsgCodes.GET_CHECK_IN_DETAIL_PARAM.getCode(), AssistantMsgCodes.GET_CHECK_IN_DETAIL_PARAM.getMsg());
        }

        //查询出档期信息
        WaveCheckInSchedule schedule = getScheduleIfNotExistInit(param.getRoomId(), param.getScheduleId(), param.getScheduleStartTime(), true);
        if (schedule == null) {
            return ResultVO.failure(AssistantMsgCodes.GET_CHECK_IN_DETAIL_NO_SCHEDULE.getCode(), AssistantMsgCodes.GET_CHECK_IN_DETAIL_NO_SCHEDULE.getMsg());
        }

        log.info("getCheckInDetail, schedule:{}", schedule);
        //根据档期开始时间和房间ID，查询出主播最近一天的打卡记录
        List<WaveCheckInRecord> waveCheckInRecords = checkInRecordManager.queryRecordsTodayWithCache(param.getRoomId(), schedule.getStartTime());
        //根据档期ID过滤打卡记录
        List<CheckInRecordBean> checkInRecordBeans = filterCheckInListByScheduleId(waveCheckInRecords, schedule.getId());
        //统计打卡记录数据合计
        CheckInTotalDataBean checkInTotalDataBean = statCheckInTotalData(checkInRecordBeans);
        //构建主持人信息
        CheckInUserInfoBean hostInfoBean = getUserInfo(schedule.getHostId());
        //构建厅主信息
        CheckInUserInfoBean njInfo = getUserInfo(schedule.getNjId());
        //构建打卡档期信息
        CheckInScheduleBean checkInScheduleBean = CheckInConvert.I.checkInSchedulePo2Bean(schedule);

        CheckInDetailVO vo = new CheckInDetailVO()
                .setTotalData(checkInTotalDataBean)
                .setHostInfo(hostInfoBean)
                .setNjInfo(njInfo)
                .setScheduleInfo(checkInScheduleBean)
                .setCheckInScheduledList(new ArrayList<>())
                .setCheckInNotScheduledList(new ArrayList<>())
                .setCheckInVersion(getCheckInVersion(schedule.getAppId(), schedule.getId()))
                ;

        // 查询用户任务相关的数据
        List<Long> recordIds = checkInRecordBeans.stream().map(CheckInRecordBean::getRecordId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(recordIds)){
            List<GetCheckInDetailsResult> checkInDetailsResultList = getCheckInDetailsByCache(recordIds, schedule.getId() );
            if (CollectionUtils.isNotEmpty(checkInDetailsResultList)){
                // 随便拿一个出来，获取到任务计算规则
                checkInDetailsResultList.stream().findFirst().map(GetCheckInDetailsResult::getCheckInUserTask).ifPresent(e -> {
                    checkInScheduleBean.setTaskRule(e.getTaskRule());
                    checkInScheduleBean.setTaskDesc(CheckInTaskEnum.getDescByType(e.getTaskRule()));
                    vo.setScheduleInfo(checkInScheduleBean);
                });


                // 定排和非定排主播列表
                Map<Integer, List<GetCheckInDetailsResult>> details
                        = checkInDetailsResultList.stream().collect(Collectors.groupingBy(e -> e.getCheckInRecord().getScheduled()));

                // 用户打卡记录表
                Map<Long, CheckInRecordBean> checkInRecordBeanMap = checkInRecordBeans.stream().collect(Collectors.toMap(CheckInRecordBean::getUserId, Function.identity(), (e1, e2) -> e1));

                if (MapUtils.isNotEmpty(details)){
                    vo.setCheckInScheduledList(CheckInConvert.I.buildCheckInDetailBeanList(details.get(CheckInScheduledConstants.SCHEDULER), checkInRecordBeanMap));
                    vo.setCheckInNotScheduledList(CheckInConvert.I.buildCheckInDetailBeanList(details.get(CheckInScheduledConstants.NOT_SCHEDULER), checkInRecordBeanMap));
                }

            }else {
                log.warn("getCheckInDetail fail or is null, scheduleId:{}, recordIds:{}", schedule.getId(), recordIds);
            }
        }

        return ResultVO.success(vo);
    }

    private Long getCheckInVersion(int appId, Long scheduleId) {
        Result<Long> result = checkInServiceRemote.getCheckInVersion(appId, scheduleId);
        return RpcResult.isSuccess(result) ? result.target(): 0L;
    }





    /**
     * 统计打卡记录数据合计
     *
     * @param waveCheckInRecords 打卡记录列表
     * @return 结果
     */
    public CheckInTotalDataBean statCheckInTotalData(List<CheckInRecordBean> waveCheckInRecords) {
        if (CollectionUtils.isEmpty(waveCheckInRecords)) {
            return CheckInTotalDataBean.noData();
        }

        //统计打卡记录数据合计
        int totalCharmValue = 0;
        int totalIncome = 0;
        int totalCheckInCount = 0;
        Set<Long> userSet = new HashSet<>();
        for (CheckInRecordBean record : waveCheckInRecords) {
            //统计打卡记录数据合计
            totalCharmValue += record.getOriginalCharmValue();
            totalIncome += record.getIncome();
            if (record.getStatus() == CheckInStatusConstant.CHECK_IN) {
                userSet.add(record.getUserId());
                totalCheckInCount++;
            }
        }
        int totalCheckInNjCount = userSet.size();
        return CheckInTotalDataBean.of(totalCheckInNjCount, totalIncome, totalCharmValue, totalCheckInCount);
    }


    /**
     * 根据档期ID过滤打卡记录
     *
     * @param waveCheckInRecords 打卡记录列表
     * @param scheduleId         档期ID
     * @return 结果
     */
    private List<CheckInRecordBean> filterCheckInListByScheduleId(List<WaveCheckInRecord> waveCheckInRecords, Long scheduleId) {
        if (CollectionUtils.isEmpty(waveCheckInRecords)) {
            return Collections.emptyList();
        }

        List<CheckInRecordBean> res = new ArrayList<>();
        //批量查询用户信息
        List<Long> userIds = waveCheckInRecords.stream().map(WaveCheckInRecord::getUserId).distinct().collect(Collectors.toList());
        Map<Long, CheckInUserInfoBean> userInfoBeanMap = batchGetUserInfo(userIds);

        //批量统计出用户今天打卡次数
        Map<Long, Long> userCheckInMap = waveCheckInRecords.stream().filter(record -> record.getStatus() == CheckInStatusConstant.CHECK_IN)
                .collect(Collectors.groupingBy(WaveCheckInRecord::getUserId, Collectors.counting()));
        for (WaveCheckInRecord record : waveCheckInRecords) {
            //统计每个主播今天打卡次数
            if (record.getScheduleId().equals(scheduleId)) {
                CheckInUserInfoBean userInfoBean = userInfoBeanMap.getOrDefault(record.getUserId(), CheckInUserInfoBean.noData(record.getUserId()));
                CheckInRecordBean checkInRecordBean = CheckInConvert.I.checkInRecordPo2Beans(record, userInfoBean);
                checkInRecordBean.setCheckInCount(userCheckInMap.getOrDefault(record.getUserId(), 0L).intValue());
                res.add(checkInRecordBean);
            }
        }
        //按收入倒序排
        res.sort(Comparator.comparing(CheckInRecordBean::getIncome).reversed());
        //避免GC有问题，提前释放
        waveCheckInRecords.clear();
        return res;
    }

    /**
     * 获取直播房间ID和家族ID
     *
     * @param liveId 直播节目ID
     * @return 结果，直播间不存在显示-1,家族ID不存在，返回0，left：直播房间ID，right：家族ID
     */
    private Optional<Pair<Long, Long>> getRoomIdAndFamilyId(long liveId, long njId) {
        Result<GetLiveResult> result = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(liveId).build());
        //直播间找不到，就不要阻塞消费队列了
        if (result.rCode() == LiveService.GET_LIVE_LOCAL_CACHE_NOT_FOUND) {
            return Optional.of(Pair.of(-1L, 0L));
        }

        if (RpcResult.isFail(result)) {
            log.error("getRoomIdAndFamilyId.getLiveByLocalCache fail, liveId:{}", liveId);
            return Optional.empty();
        }

        Result<FamilyInfoBean> familyRes = familyService.getUserInFamily(njId);
        //查询失败
        if (familyRes.rCode() == FamilyService.GET_USER_IN_FAMILY_PARAM_FAIL) {
            log.error("getRoomIdAndFamilyId.getUserInFamily fail, njId:{}", njId);
            return Optional.empty();
        }

        long familyId = 0;
        if (familyRes.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            familyId = familyRes.target().getId();
        }
        return Optional.of(Pair.of(result.target().getLive().getLiveRoomId(), familyId));
    }

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    private CheckInUserInfoBean getUserInfo(long userId) {
        Result<GetSimpleUserResult> result = userService.getSimpleUserByCache(userId);
        if (RpcResult.noBusinessData(result)) {
            log.warn("CheckInManager.getUserInfo fail, userId:{}", userId);
            return CheckInUserInfoBean.noData(userId);
        }

        String cdnHost = commonProviderConfig.getBusinessConfig(ContextUtils.getBusinessEvnEnum().getAppId()).getCdnHost();
        return CheckInConvert.I.simpleUser2UserInfoBean(result.target().getSimpleUser(), cdnHost);
    }

    /**
     * 获取用户信息
     *
     * @param userIds 用户ID
     * @return 结果map， key：用户ID，value：用户信息
     */
    private Map<Long, CheckInUserInfoBean> batchGetUserInfo(List<Long> userIds) {
        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(userIds).build();
        Result<BatchGetSimpleUserResult> result = userService.batchGetSimpleUserByCache(userParam);
        if (RpcResult.noBusinessData(result)) {
            log.warn("CheckInManager.batchGetUserInfo fail, userId:{}", userIds);
            return Collections.emptyMap();
        }

        String cdnHost = commonProviderConfig.getBusinessConfig(ContextUtils.getBusinessEvnEnum().getAppId()).getCdnHost();
        List<CheckInUserInfoBean> checkInUserInfoBeans = CheckInConvert.I.simpleUserList2UserInfoBeans(result.target().getUserList(), cdnHost);
        return checkInUserInfoBeans.stream().collect(Collectors.toMap(CheckInUserInfoBean::getUserId, bean -> bean));
    }

    private Map<Long, SimpleUser> batchGetUserInfoMap(List<Long> userIds) {
        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(userIds).build();
        Result<BatchGetSimpleUserResult> result = userService.batchGetSimpleUserByCache(userParam);
        if (RpcResult.noBusinessData(result)) {
            log.warn("CheckInManager.batchGetUserInfoMap fail, userId:{}", userIds);
            return Collections.emptyMap();
        }
        return result.target().getUserList().stream().collect(Collectors.toMap(SimpleUser::getUserId, user -> user, (first, second) -> first));
    }


    private ResultVO<Void> checkParam(Long scheduleId, Long scheduleStartTime, Long roomId, String remark){
        //参数校验
        if (scheduleId == null && scheduleStartTime == null) {
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PARAM.getCode(), AssistantMsgCodes.CHECK_IN_PARAM.getMsg());
        }

        //参数校验
        if (roomId < 0) {
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PARAM.getCode(), AssistantMsgCodes.CHECK_IN_PARAM.getMsg());
        }

        if (remark != null && remark.getBytes(StandardCharsets.UTF_8).length / 3 > assistantConfig.getRemarkMaxLength()) {
            String msg = AssistantMsgCodes.CHECK_IN_REMARK_TOO_LONG.getMsg().replace("#{length}", String.valueOf(assistantConfig.getRemarkMaxLength()));
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_REMARK_TOO_LONG.getCode(), msg);
        }

        DateTime yesterday = cn.hutool.core.date.DateUtil.offsetDay(cn.hutool.core.date.DateUtil.beginOfHour(new Date()), -1);
        int compare = cn.hutool.core.date.DateUtil.compare(new Date(scheduleStartTime), yesterday);
        if (compare <= 0) {
            log.info("checkParam cannot operate schedule, scheduleStartTime:{}, scheduleId:{}, roomId:{} ", scheduleStartTime, scheduleId, roomId);
            // 超24小时不允许调账
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CANNOT_OPERATE_SCHEDULE);
        }

        return ResultVO.success();
    }



    /**
     * 档期结束了的打卡，就需要校验打卡数据合法性
     *
     * @param waveCheckInRecords 打卡记录列表
     * @param checkInBeanMap     打卡记录参数map
     * @param newUserCheckInList 新增用户记录
     * @return 结果
     */
    private ResultVO<Void> checkDataInvalidByOperate(List<WaveCheckInRecord> waveCheckInRecords, Map<Long, CheckInOperateInfoBean> checkInBeanMap, List<CheckInNewUserOperateBean> newUserCheckInList) {
        //调账后的魅力值差额
        int charmDiffValue = 0;
        if (CollectionUtils.isNotEmpty(waveCheckInRecords)) {
            for (WaveCheckInRecord record : waveCheckInRecords) {
                CheckInOperateInfoBean checkInBean = checkInBeanMap.get(record.getId());
                if (checkInBean == null) {
                    continue;
                }
                charmDiffValue += checkInBean.getCharmDiffValue();
            }
        }

        if (newUserCheckInList != null) {
            newUserCheckInList.removeIf(bean -> bean.getCharmDiffValue() == 0 && bean.getStatus() == CheckInStatusConstant.NO_CHECK_IN && !bean.getScheduled());
            long sum = newUserCheckInList.stream()
                    .mapToLong(CheckInNewUserOperateBean::getCharmDiffValue)
                    .sum();// 计算总和
            charmDiffValue += (int) sum;
        }

        //库里魅力值总和和调账后的魅力值总和不相等，有问题
        if (charmDiffValue != 0) {
            log.warn("CheckInManager.checkDataInvalidByOperate exist un allocate charm, unAllocateCharm:{}", charmDiffValue);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CHARM_EXIST_UN_ALLOCATE.getCode(), AssistantMsgCodes.CHECK_IN_CHARM_EXIST_UN_ALLOCATE.getMsg());
        }

        return ResultVO.success();
    }

    /**
     * 同步档期主持人
     */
    public void syncScheduleHost(Date startTime, Date endTime) {
        //查询出最近1小时的档期，且主持人ID为空的
        List<WaveCheckInSchedule> scheduleList = checkInScheduleManager.getNoHostScheduleList(startTime, endTime);
        if (CollectionUtils.isEmpty(scheduleList)) {
            return;
        }

        AtomicInteger totalSuccess = new AtomicInteger();
        //按业务线分组，异步操作
        Map<Integer, List<WaveCheckInSchedule>> businessMap = scheduleList.stream().collect(Collectors.groupingBy(WaveCheckInSchedule::getAppId));
        CountDownLatchWrapper countDownLatchWrapper = new CountDownLatchWrapper(THREAD_POOL_EXECUTOR, 3 * TimeConstant.ONE_MINUTE, businessMap.size());
        for (Map.Entry<Integer, List<WaveCheckInSchedule>> entry : businessMap.entrySet()) {
            List<WaveCheckInSchedule> schedules = entry.getValue();
            Integer appId = entry.getKey();
            //丢到线程池
            countDownLatchWrapper.submit(() -> {
                try {
                    int successCount = checkInScheduleManager.batchConfirmAndUpdateScheduleHost(appId, schedules);
                    totalSuccess.addAndGet(successCount);
                } catch (Exception e) {
                    log.error("syncScheduleHost error, appId:{}", entry.getKey(), e);
                }
            });
        }
        countDownLatchWrapper.await();
        log.info("syncScheduleHost success, totalTaskCount:{}, totalSuccess:{}", scheduleList.size(), totalSuccess.get());
    }



    /**
     */
    public WaveCheckInSchedule getScheduleIfNotExistInit(long roomId, Long scheduleId, Long hourStartTime, long njId, boolean initHostByConfig ) {
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        WaveCheckInSchedule schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(roomId, scheduleId, hourStartTime);
        if (schedule != null) {
            return schedule;
        }
        //查询出档期信息
        scheduleId = initSchedule(appId, roomId, hourStartTime, njId, initHostByConfig);
        if (scheduleId < 0) {
            return null;
        }
        return checkInScheduleManager.getScheduleById(scheduleId);
    }


    private Long getNjIdByRoomId(Long roomId){
        Result<GetLiveRoomResult> resp = liveRoomService.getLiveRoom(GetLiveRoomParam.builder()
                .liveRoomId(roomId)
                .build());

        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getNjIdByRoomId error, liveRoomId: {}, rCode: {}, message: {}", roomId, resp.rCode(), resp.getMessage());
            return 0L;
        }
        // 房主id
        return Optional.ofNullable(resp.target()).map(GetLiveRoomResult::getLiveRoom).map(LiveRoom::getUserId).orElse(0L);
    }


    /**
     * 新增档期主持人
     *
     * @param userId 操作人
     * @param param
     * @return
     */
    public ResultVO<Void> operateHost(long userId, CheckInOperateHostParam param) {

        Long roomId = param.getRoomId();
        // 房主id
        Long njId = getNjIdByRoomId(roomId);
        if (njId <= 0L) {
            log.warn("getLiveRoom error, liveRoomId: {}", roomId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_LIVE_NO_EXIST);
        }

        //获取档期信息
        WaveCheckInSchedule schedule = getScheduleIfNotExistInit(roomId, param.getScheduleId(), param.getScheduleStartTime(), njId, false);
        if (schedule == null) {
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NO_SCHEDULE.getCode(), AssistantMsgCodes.CHECK_IN_NO_SCHEDULE.getMsg());
        }
        // 当前档期的主持人
        Long hostId = schedule.getHostId();
        //被操作的人
        Long targetUserId = param.getTargetUserId();

        DateTime yesterday = cn.hutool.core.date.DateUtil.offsetDay(cn.hutool.core.date.DateUtil.beginOfHour(new Date()), -1);
        int compare = cn.hutool.core.date.DateUtil.compare(schedule.getStartTime(), yesterday);
        if (compare <= 0) {
            // 超24小时不允许操作主持
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OP_HOST_ERROR);
        }

        boolean isCurrentSchedule = cn.hutool.core.date.DateUtil.isIn(new Date(param.getScheduleStartTime()), cn.hutool.core.date.DateUtil.beginOfHour(new Date()), cn.hutool.core.date.DateUtil.endOfHour(new Date()));

        ResultVO<Void> checkOperateHostParamResult = checkOperateHostParam(njId, hostId, userId, targetUserId, param.getOp(), isCurrentSchedule);
        if (!checkOperateHostParamResult.isOK()) {
            return checkOperateHostParamResult;
        }
        // 将被操作人设置为档期主持人
        Result<Boolean> result = checkInServiceRemote.updateScheduleHost(schedule.getId(), targetUserId);
        if (RpcResult.isFail(result)) {
            log.warn("updateScheduleHost fail, scheduleId:{}, targetUserId:{}", schedule.getId(), targetUserId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OP_HOST_ERROR);
        }

        if (result.target()) {
            // 更新成功删除redis缓存
            checkInRedisDao.delScheduleCache(param.getRoomId(), schedule.getStartTime().getTime());
        }
        return result.target() ? ResultVO.success() : ResultVO.alertFailure(AssistantMsgCodes.CHECK_IN_OP_HOST_ERROR);
    }


    /**
     * 判断当前用户是不是当前厅有签约关系
     * 1. 签约主播
     * 2. 厅主
     * 3. 当前厅的家族长
     *
     * @param userId
     * @return
     */
    public boolean curRoomSignRelationship(Long userId, Long njId) {
        if (userId.equals(njId)) {
            // 厅主
            return true;
        }
        //判断 被操作人是否是签约主播
        Result<Long> playerSignRes = familyService.playerCurSignNj(userId);
        if (playerSignRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return false;
        }
        return playerSignRes.target().equals(njId);
    }


    /**
     * 1. 判断被操作用户是否是 签约主播
     * 2. 操作人权限：是否是 房主、或者当前档期的主持人
     *
     * @param njId              房主id
     * @param hostId            当前档期主持人id
     * @param opUserId          操作
     * @param targetUserId      被操作人id
     * @param isCurrentSchedule
     * @return
     */
    private ResultVO<Void> checkOperateHostParam(Long njId, Long hostId, Long opUserId, Long targetUserId, Integer op, boolean isCurrentSchedule) {

        if (targetUserId.equals(hostId)) {
            // 被操作者已经是主持了
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OP_HOST_IS_HOST);
        }

        //被操作用户是否是签约主播
        boolean targetUserIsCurRoomSignPlayer = curRoomSignRelationship(targetUserId, njId);
        if (!targetUserIsCurRoomSignPlayer) {
            return ResultVO.alertFailure(AssistantMsgCodes.CHECK_IN_NO_SIGN_PLAYER);
        }

        if (!isCurrentSchedule){
            if (CheckInOperateHostEnum.TRANSFER_HOST.getOp().equals(op)) {
                if (opUserId.equals(hostId) || njId.equals(opUserId)) {
                    //如果是转移主持 当前操作人需要是 主持人/ 房主
                    return ResultVO.success();
                }
                return ResultVO.alertFailure(AssistantMsgCodes.CHECK_IN_OP_HOST_NO_PERMISSION);
            }
        }



        if (CheckInOperateHostEnum.SET_HOST.getOp().equals(op)) {
            boolean canOp = njId.equals(opUserId);
            //  当  操作人是 房主   或者  当前档期主持人为空时，当前操作用户为 签约主播可设置主持人
            canOp = canOp || (hostId == null || hostId <= 0) && curRoomSignRelationship(opUserId, njId);
            if (canOp) {
                return ResultVO.success();
            }
            if (hostId != null && hostId > 0){
                // 已经存在主持人，设置失败
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NOT_PERMISSION_OP_HOST);
            }
        }

        return ResultVO.alertFailure(AssistantMsgCodes.CHECK_IN_OP_HOST_NO_ROLE);
    }

    /**
     * 排班打卡校验
     *
     * @param schedule 档期信息
     * @return 结果
     */
    private ResultVO<Void> checkInCheck(WaveCheckInSchedule schedule) {
        if (schedule.getHostId() != null && schedule.getHostId() > 0) {
            if (!schedule.getHostId().equals(ContextUtils.getContext().getUserId())) {
                //如果已经有打卡主持，但是不是当前的人，那么说明没有权限修改这张卡
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NO_PERMISSION_RETRY.getCode(), AssistantMsgCodes.CHECK_IN_NO_PERMISSION_RETRY.getMsg());
            }
            //如果已经有打卡主持，且是当前的用户，则有权限打卡
            return ResultVO.success();
        }
        if (schedule.getHostId() == null) {
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NO_HOST.getCode(), AssistantMsgCodes.CHECK_IN_NO_HOST.getMsg());
        }
        return ResultVO.success();
    }


    /**
     * 校验等待删除的记录是否有问题
     *
     * @return true 没有问题，false: 有问题
     */
    private ResultVO<Void> checkWaitDelRecord( List<Long> waitDelRecordIds) {
        if (CollectionUtils.isEmpty(waitDelRecordIds)) {
            return ResultVO.success();
        }

        //查询类型是否可删除的
        List<WaveCheckInRecord> records = checkInRecordManager.queryRecordListByIds(waitDelRecordIds);
        if (CollectionUtils.isEmpty(records)) {
            log.warn("checkWaitDelRecord. records is not exist. waitDelRecordIds: {}", waitDelRecordIds);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NO_RECORD_DEL);
        }

        //校验是否有不能删除的数据
        boolean res = records.stream().anyMatch(
                record -> (record.getSourceType() == CheckInSourceTypeConstant.SYSTEM_ADD
                        || record.getSourceType() == CheckInSourceTypeConstant.HOST_TO_SYSTEM)
                        // 定排用户不允许删除
                        || (record.getScheduled() != null && CheckInScheduledConstants.SCHEDULER == record.getScheduled() && record.getCharmValue() != 0)
        );
        if (res) {
            log.warn("checkWaitDelRecord has cannot delete record. waitDelRecordIds: {}", waitDelRecordIds);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NJ_HAS_INCOME);
        }
        return ResultVO.success();
    }

    /**
     * 获取麦序福利配置
     */
    public ResultVO<GetCheckInConfigResult> getConfig(Long roomId, int appId) {

        Long njId = getNjIdByRoomId(roomId);
        if (njId <= 0L) {
            log.warn("getConfig getLiveRoom error, liveRoomId: {}", roomId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_LIVE_NO_EXIST);
        }

        long familyId = 0;
        //查询房间ID和家族ID
        Result<FamilyInfoBean> familyRes = familyService.getUserInFamily(njId);
        //查询失败
        if (familyRes.rCode() == FamilyService.GET_USER_IN_FAMILY_PARAM_FAIL) {
            log.error("getConfig.getUserInFamily fail, njId:{}", njId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_GET_CONFIG_FAIL);
        }

        if (RpcResult.isSuccess(familyRes)) {
            familyId = familyRes.target().getId();
        }

        Result<GetCheckInConfigResult> result = checkInServiceRemote.getConfig(roomId, appId, familyId);
        if (RpcResult.isFail(result)){
            log.error("getConfig fail, roomId:{}", roomId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_GET_CONFIG_FAIL);
        }

        return ResultVO.success(result.target());
    }

    /**
     * 麦序福利 2.0 保存麦序表
     */
    public ResultVO<Void> checkInOperate(CheckInOperateParam param) {

        Long userId = ContextUtils.getContext().getUserId();

        //校验参数
        ResultVO<Void> checkParam = checkParam(param.getRoomId(), param.getScheduleId(), param.getScheduleStartTime(), param.getRemark());
        if (!checkParam.isOK()) {
            return checkParam;
        }


        // 通过前面的校验，再走下面的校验
        if (param.getInfoList() != null) {
            boolean hasStatusInvalid = param.getInfoList().stream()
                    .anyMatch(record -> record.getStatus() < CheckInStatusConstant.NO_CHECK_IN || record.getStatus() > CheckInStatusConstant.CHECK_IN);
            if (hasStatusInvalid) {
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PARAM.getCode(), AssistantMsgCodes.CHECK_IN_PARAM.getMsg());
            }
        }

        // 校验是否可以修改任务进度
        ResultVO<Void> checkTaskProcess = checkTaskProcess(param);
        if (!checkTaskProcess.isOK()){
            return checkTaskProcess;
        }

        // 校验一些权限
        ResultVO<Void> checkPermissionResult = checkPermission(param, userId);
        if (!checkPermissionResult.isOK()){
            return checkPermissionResult;
        }


        //查询出档期信息
        WaveCheckInSchedule schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(param.getRoomId(), param.getScheduleId(), param.getScheduleStartTime());
        if (schedule == null) {
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NO_SCHEDULE.getCode(), AssistantMsgCodes.CHECK_IN_NO_SCHEDULE.getMsg());
        }
        //补上档期ID
        param.setScheduleId(schedule.getId());

        if (getCheckInVersion(ContextUtils.getBusinessEvnEnum().getAppId(), param.getScheduleId()) > param.getCheckInVersion()){
            //版本号不一致，直接中断流程
            log.warn("checkInOperateCore version fail, scheduleId:{}", schedule.getId());
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_SAVE_VERSION_FAIL);
        }

        ResultVO<Void> checkRes = checkInCheck(schedule);
        if (!checkRes.isOK()) {
            //校验不通过，直接中断流程
            return checkRes;
        }
        //麦序福利核心逻辑
        return checkInOperateCore(schedule, param);

    }

    /**
     * 校验权限
     *
     * @param param
     * @param userId
     * @return
     */
    private ResultVO<Void> checkPermission(CheckInOperateParam param, Long userId) {
        ResultVO<GetCheckInConfigResult> configResult = getConfig(param.getRoomId(), ContextUtils.getBusinessEvnEnum().getAppId());
        if (!configResult.isOK()){
            log.warn("checkPermission getConfig fail, roomId:{}", param.getRoomId());
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_GET_CONFIG_FAIL);
        }

        GetCheckInConfigResult config = configResult.getData();
        Long njId = getNjIdByRoomId(param.getRoomId());

        // 检查调账权限
        if (CheckInAdjustCharmAuthTypeEnum.NOT.getType().equals(config.getAdjustCharmAuthType())){
            // 不可调账
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OPERATE_PERMISSION_FAIL);
        }

        if (CheckInAdjustCharmAuthTypeEnum.NJ_AND_HOST_SCHEDULE.getType().equals(config.getAdjustCharmAuthType())){
            // 厅主&当档主持可调账
            if (!userId.equals(param.getHostId()) && !userId.equals(njId)){
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OPERATE_PERMISSION_FAIL);
            }
        }

        if (CheckInAdjustCharmAuthTypeEnum.NJ.getType().equals(config.getAdjustCharmAuthType())){
            // 厅主可调账
            if (!userId.equals(njId)){
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_OPERATE_PERMISSION_FAIL);
            }
        }


        // 校验非定排主播是否勾选了麦序
        if (CollUtil.isNotEmpty(param.getInfoList())){
            boolean hasNotSchedule = param.getInfoList().stream().anyMatch(record -> !record.getScheduled() && record.getStatus().equals(CheckInStatusConstant.CHECK_IN));
            if (hasNotSchedule){
                log.warn("checkInOperateCore not schedule cannot check in by info list, scheduleId:{}", param.getScheduleId());
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NOT_SCHEDULE_CANNOT_CHECK_IN);
            }
        }
        if (CollUtil.isNotEmpty(param.getNewUserCheckInList())){
            boolean hasNotSchedule = param.getNewUserCheckInList().stream().anyMatch(record -> !record.getScheduled() && record.getStatus().equals(CheckInStatusConstant.CHECK_IN));
            if (hasNotSchedule){
                log.warn("checkInOperateCore not schedule cannot check in by new user list, scheduleId:{}", param.getScheduleId());
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_NOT_SCHEDULE_CANNOT_CHECK_IN);
            }
        }


        return ResultVO.success();
    }

    private ResultVO<Void> checkTaskProcess(CheckInOperateParam param) {
        Boolean enableOperateTaskProgressDiff = assistantConfig.getEnableOperateTaskProgressDiff();
        boolean isCurrentSchedule = cn.hutool.core.date.DateUtil.isIn(new Date(param.getScheduleStartTime()), cn.hutool.core.date.DateUtil.beginOfHour(new Date()), cn.hutool.core.date.DateUtil.endOfHour(new Date()));

        for (CheckInOperateInfoBean infoBean : param.getInfoList()) {

            if (infoBean.getTaskProgress() > (infoBean.getOriginalValue() + infoBean.getCharmDiffValue())){
                log.warn("check task process fail, taskProgress is greater than charm. infoBean:{}", infoBean);
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PROCESS_GTE_CHARM);
            }

            // 不是当前档期，不允许修改本档任务
            if (infoBean.getTaskScoreDiff() != 0 && !isCurrentSchedule){
                log.warn("check task process fail. not current schedule. cannot operate task score. infoBean:{}", infoBean);
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CANNOT_OPERATE_TASK_SCORE);
            }

            // 非定排主播，不允许调整任务分.  目前先由前端控制 @liweijian
//            if (infoBean.getTaskScoreDiff() != 0 && !infoBean.getScheduled()){
//                log.warn("check task process fail. notSchedule cannot operate task score. infoBean:{}", infoBean);
//                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CANNOT_OPERATE_TASK_SCORE_BY_NOT_SCHEDULE);
//            }
        }

        for (CheckInNewUserOperateBean newUserOperateBean : param.getNewUserCheckInList()) {

            if (newUserOperateBean.getTaskProgress() > (newUserOperateBean.getOriginalValue() + newUserOperateBean.getCharmDiffValue())){
                log.warn("check task process fail, taskProgress is greater than charm. newUserOperateBean:{}", newUserOperateBean);
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PROCESS_GTE_CHARM);
            }

            // 不是当前档期，不允许修改本档任务
            if (newUserOperateBean.getTaskScoreDiff() != 0 && !isCurrentSchedule){
                log.warn("check task process fail. not current schedule. cannot operate task score. newUserOperateBean:{}", newUserOperateBean);
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CANNOT_OPERATE_TASK_SCORE);
            }
            // 任务进度调整. 目前先由前端控制 @liweijian
//            if (!enableOperateTaskProgressDiff && newUserOperateBean.getTaskProgressDiff() > 0){
//                log.warn("check task process fail, taskProcessDiff:{}, newUserOperateBean:{}", newUserOperateBean.getTaskProgressDiff(), newUserOperateBean);
//                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_CANNOT_OPERATE_TASK_PROCESS);
//            }
        }

        return ResultVO.success();
    }


    /**
     * 麦序福利核心逻辑
     *
     * @param schedule 档期信息
     * @param param    参数
     * @return 结果
     */
    private ResultVO<Void> checkInOperateCore(WaveCheckInSchedule schedule, CheckInOperateParam param) {
        try (RedisLock lock = checkInRedisDao.getAddCharmLock(schedule.getRoomId())) {
            if (!lock.tryLock()) {
                log.warn("checkInOperateCore lock fail, scheduleId:{}", schedule.getId());
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_PARAM.getCode(), AssistantMsgCodes.CHECK_IN_PARAM.getMsg());
            }

            List<WaveCheckInRecord> waveCheckInRecords = checkInRecordManager.queryRecordsByScheduleId(schedule.getId());
            //校验是否有记录的原始魅力值发生了变化
            Map<Long, CheckInOperateInfoBean> checkInBeanMap = new HashMap<>();
            if (param.getInfoList() != null) {
                checkInBeanMap = param.getInfoList().stream().collect(Collectors.toMap(CheckInOperateInfoBean::getRecordId, bean -> bean, (e1, e2) -> e1));
            }

            //校验是否能删除这些记录
            ResultVO<Void> waitDelRes = checkWaitDelRecord(param.getWaitDelRecordIds());
            if (!waitDelRes.isOK()) {
                return waitDelRes;
            }

            //校验魅力值总和是否能对的上
            ResultVO<Void> invalidRes = checkDataInvalidByOperate(waveCheckInRecords, checkInBeanMap, param.getNewUserCheckInList());
            if (!invalidRes.isOK()) {
                return invalidRes;
            }

            //处理打卡相关的数据
            Result<Void> result = checkInServiceRemote.checkInOperateCore(CheckInConvert.I.convertRequestCheckInOperateCore(param, schedule.getAppId(), ContextUtils.getContext().getUserId()));
            if (result.rCode() == WaveCheckInService.CHECK_IN_OPERATE_CORE_UNDONE_SETTLEMENT) {
                // 未完成结算，直接中断流程
                log.warn("checkInOperateCore fail, undone settlement is running. cannot operate. scheduleId:{}", schedule.getId());
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_UNFINISHED_SETTLEMENT);
            }


            if (RpcResult.isFail(result)) {
                log.error("checkInOperateCore fail, scheduleId:{}, rCode: {}", schedule.getId(), result.rCode());
                return ResultVO.failure(AssistantMsgCodes.CHECK_IN_FAIL.getCode(), AssistantMsgCodes.CHECK_IN_FAIL.getMsg());
            }
            //删除redis缓存
            checkInRedisDao.delRecordCache(param.getRoomId(), schedule.getStartTime());
            checkInRedisDao.delScheduleCache(param.getRoomId(), schedule.getStartTime().getTime());
            return ResultVO.success();
        } catch (Exception e) {
            log.error("checkInOperateCore error, scheduleId:{}", schedule.getId(), e);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_FAIL.getCode(), AssistantMsgCodes.CHECK_IN_FAIL.getMsg());
        }finally {
            // 删除缓存
            checkInRedisDao.delCheckInDetailsCache(param.getScheduleId());
        }
    }


    /**
     * 分页获取收光记录列表
     */
    public ResultVO<PageVo<PageLightGiftRecordVo>> getLightGiftList(Long roomId, Long scheduleId, Integer pageNo, Integer pageSize) {
        Result<PageBean<CheckInLightGiftRecordBean>> result =
                checkInServiceRemote.getLightGiftList(roomId, scheduleId, pageNo, pageSize, ContextUtils.getBusinessEvnEnum().getAppId());

        if (RpcResult.isFail(result)){
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_GET_LIGHT_GIFT_LIST_FAIL);
        }

        PageBean<CheckInLightGiftRecordBean> pageBean = result.target();
        List<CheckInLightGiftRecordBean> recordList = pageBean.getList();
        List<Long> sendUserIds = recordList.stream().map(CheckInLightGiftRecordBean::getSendUserId).collect(Collectors.toList());
        List<Long> recUserIds = recordList.stream().map(CheckInLightGiftRecordBean::getRecUserId).collect(Collectors.toList());
        List<Long> allUserIds = Stream.concat(sendUserIds.stream(), recUserIds.stream())
                .distinct()
                .collect(Collectors.toList());

        // 已经有 allUserIds，释放掉其他的用户 ID 列表
        sendUserIds.clear();
        recUserIds.clear();

        Map<Long, SimpleUser> allUserMap = batchGetUserInfoMap(allUserIds);

        List<PageLightGiftRecordVo> pageResult = recordList.stream().map(record ->
                CheckInConvert.I.buildPageLightGiftRecordVo(record,
                    MapUtil.get(allUserMap, record.getSendUserId(), SimpleUser.class),
                    MapUtil.get(allUserMap, record.getRecUserId(), SimpleUser.class)
        )).collect(Collectors.toList());

        return ResultVO.success(PageVo.of(pageBean.getTotal(), pageResult));
    }

    /**
     * 获取我的任务列表
     */
    public ResultVO<List<CheckInMyTaskBean>> getMyTask(long userId, Long roomId, int appId, String date) {
        Result<List<CheckInMyTaskBean>> myTaskList = checkInServiceRemote.getMyTask(userId, roomId, appId, date);

        if (RpcResult.isFail(myTaskList)){
            log.warn("getMyTask fail, userId:{}, roomId:{}", userId, roomId);
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_GET_MY_TASK_FAIL);
        }

        return ResultVO.success(myTaskList.target());
    }


    public ResultVO<Boolean> checkHost(long userId, Long roomId, Long scheduleStartTime) {
        if (roomId < 0 ||  scheduleStartTime == null) {
            return ResultVO.failure(AssistantMsgCodes.GET_CHECK_IN_DETAIL_PARAM);
        }

        long scheduleId = 0L;
        WaveCheckInSchedule schedule;

        //查询出档期信息
        if (assistantConfig.isInitScheduleByCheckHost()){
            schedule = getScheduleIfNotExistInit(roomId, scheduleId, scheduleStartTime, true);
        }else {
            schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(roomId, scheduleId, scheduleStartTime);
        }

        if (schedule == null) {
            log.info("checkHost. schedule is null. host is false. roomId:{}, scheduleStartTime:{}", roomId, scheduleStartTime);
            return ResultVO.success(false);
        }

        return ResultVO.success(schedule.getHostId() != null && schedule.getHostId().equals(userId));
    }

    /**
     * 获取待分配的全麦奖励
     */
    public ResultVO<GetCheckInAllMicNotAllocationResult> getAllMicNotAllocation(Long roomId, Long scheduleStartTime, long userId, int appId) {
        if (null == scheduleStartTime || scheduleStartTime <= 0){
            log.warn("getAllMicNotAllocation param scheduleStartTime is null. roomId={}", roomId);
            return ResultVO.failure(AssistantMsgCodes.GET_CHECK_IN_DETAIL_PARAM);
        }

        WaveCheckInSchedule schedule = checkInScheduleManager.getScheduleInfoByIdOrTime(roomId, null, scheduleStartTime);
        if (schedule == null) {
            log.warn("getAllMicNotAllocation schedule is null. roomId={}, scheduleStartTime={}", roomId, scheduleStartTime);
            return ResultVO.success(null);
        }

        Long scheduleId = schedule.getId();
        // 非主持或者非厅主，不返回
        if (!schedule.getNjId().equals(userId) && userId != schedule.getHostId()) {
            log.warn("getAllMicNotAllocation userId is not host or nj, scheduleId:{}, userId:{}",scheduleId, userId);
            return ResultVO.success(null);
        }

        Result<List<CheckInAllMicGiftRecordBean>> result = checkInServiceRemote.getAllMicNotAllocation(new GetAllMicGiftNotAllocationParam()
                .setAppId(appId).setRoomId(roomId).setScheduleId(scheduleId).setUserId(userId)
        );

        if (RpcResult.isFail(result)){
            log.warn("getAllMicNotAllocation fail, result:{}", result);
            return ResultVO.failure(result.rCode(), result.getMessage());
        }

        List<CheckInAllMicGiftRecordBean> target = result.target();
        if (CollectionUtils.isEmpty(target)) {
            return ResultVO.success(null);
        }

        List<Long> sendUserIds = target.stream().map(CheckInAllMicGiftRecordBean::getSendUserId).distinct().collect(Collectors.toList());
        Map<Long, SimpleUser> sendUserMap = batchGetUserInfoMap(sendUserIds);

        List<CheckInAllMicNotAllocationVo> recordList = target.stream()
                .map(record ->
                        CheckInConvert.I.buildCheckInAllMicNotAllocationVo(record, MapUtil.get(sendUserMap, record.getSendUserId(), SimpleUser.class)))
                .collect(Collectors.toList());

        return ResultVO.success(new GetCheckInAllMicNotAllocationResult()
                .setList(recordList)
                .setHostId(schedule.getNjId().equals(schedule.getHostId()) ? 0L : schedule.getHostId())
        );
    }


    /**
     * 分页获取全麦记录列表
     */
    public ResultVO<PageVo<PageCheckInAllMicRecordVo>> getAllMicList(Long roomId, Long scheduleId, int appId, Integer pageNo, Integer pageSize) {

        Result<PageBean<CheckInAllMicGiftRecordBean>> result = checkInServiceRemote.getAllMicGiftRecordList(roomId, scheduleId, pageNo, pageSize, appId);
        if (RpcResult.isFail(result)){
            log.warn("getAllMicList fail, result:{}", result);
            return ResultVO.failure(AssistantMsgCodes.GET_CHECK_IN_ALL_MIC_LIST_FAIL);
        }

        PageBean<CheckInAllMicGiftRecordBean> pageBean = result.target();
        List<CheckInAllMicGiftRecordBean> recordList = pageBean.getList();
        List<Long> sendUserIds = recordList.stream().map(CheckInAllMicGiftRecordBean::getSendUserId).collect(Collectors.toList());
        List<Long> allocationUserIds = recordList.stream().map(CheckInAllMicGiftRecordBean::getAllocationUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> allUserIds = Stream.concat(sendUserIds.stream(), allocationUserIds.stream())
               .distinct()
               .collect(Collectors.toList());
        // 已经有 allUserIds，释放掉其他的用户 ID 列表
        sendUserIds.clear();
        allocationUserIds.clear();

        Map<Long, SimpleUser> allUserMap = batchGetUserInfoMap(allUserIds);
        List<PageCheckInAllMicRecordVo> pageResult = recordList.stream().map(record ->
                CheckInConvert.I.buildPageCheckInAllMicRecordVo(record,
                        MapUtil.get(allUserMap, record.getSendUserId(), SimpleUser.class),
                        MapUtil.get(allUserMap, record.getAllocationUserId(), SimpleUser.class)
                )).collect(Collectors.toList());

        return ResultVO.success(PageVo.of(pageBean.getTotal(), pageResult));
    }


    /**
     * 全麦分配
     */
    public ResultVO<Void> allMicGiftAllocation(Long allMicId, Long allocationUserId, Long operatorUserId){

        Result<Void> result = checkInServiceRemote.allMicGiftAllocation(allMicId, allocationUserId, operatorUserId);
        if (result.rCode() == CheckInServiceRemote.ALL_MIC_GIFT_ALLOCATION_NOT_PERMISSION){
            // 无权限
            log.warn("checkInManager all mic gift allocation is not permission. allMicId: {}, allocationUserId: {}, operatorUserId: {}, rCode:{}",
                    allMicId, allocationUserId, operatorUserId, result.rCode());
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_ALLOCATION_NOT_PERMISSION);
        }

        if (RpcResult.isFail(result)){
            log.warn("checkInManager all mic gift allocation fail. allMicId: {}, allocationUserId: {}, operatorUserId: {}, rCode:{}",
                    allMicId, allocationUserId, operatorUserId, result.rCode());
            return ResultVO.failure(AssistantMsgCodes.CHECK_IN_ALLOCATION_FAIL);
        }

        return ResultVO.success(null);
    }


    /**
     * 获取打卡延迟秒数
     * 如果Redis中不存在，则生成一个随机值并存入Redis
     *
     * @return 延迟秒数
     */
    public int getCheckInDelaySec(Long roomId, Long njId) {
        // 先从Redis中获取
        int delaySec = checkInRedisDao.getCheckInDelaySec(roomId);
        if (delaySec >= 0) {
            log.info("Get check-in delay from Redis: {} seconds", delaySec);
            return delaySec;
        }

        // Redis中不存在，需要生成一个随机值并存入Redis
        try (RedisLock lock = checkInRedisDao.getCheckInDelaySecLock(roomId)) {
            if (!lock.lock()) {
                log.warn("Failed to acquire lock for generating check-in delay");
                // 如果获取锁失败，再次尝试从Redis获取，可能其他线程已经生成了
                delaySec = checkInRedisDao.getCheckInDelaySec(roomId);
                return Math.max(delaySec, 0);
            }

            // 获取锁成功后，再次检查Redis中是否已有值（双重检查）
            delaySec = checkInRedisDao.getCheckInDelaySec(roomId);
            if (delaySec >= 0) {
                log.info("Get check-in delay from Redis after lock: {} seconds", delaySec);
                return delaySec;
            }

            // 生成随机延迟秒数
            int maxDelaySec = assistantConfig.getCheckInMaxDelaySec();
            delaySec = RandomUtils.nextInt(0, maxDelaySec+1);

            // 存入Redis
            checkInRedisDao.setCheckInDelaySec(delaySec, roomId);
            return delaySec;
        }
    }

    /**
     * 从缓存中获取打卡详情
     */
    private List<GetCheckInDetailsResult> getCheckInDetailsByCache(List<Long> recordIds, Long scheduleId) {

        if (!assistantConfig.isEnableCheckInDetailCache()){
            log.debug("get checkInDetailsByCache is disabled.");
            return getCheckInDetails(recordIds, scheduleId);
        }

        List<GetCheckInDetailsResult> cachedResults = checkInRedisDao.getCheckInDetailsCache(scheduleId);
        if (CollectionUtils.isNotEmpty(cachedResults)) {
            log.debug("getCheckInDetailsByCache hit cache. scheduleId: {}, recordIds: {}", scheduleId, recordIds);
            return cachedResults;
        }

        try (RedisLock lock = checkInRedisDao.getCheckInDetailsLock(scheduleId)) {
            if (!lock.lock()) {
                log.warn("getCheckInDetailsByCache acquire lock failed. scheduleId: {}, recordIds: {}", scheduleId, recordIds);
                return Collections.emptyList();
            }

            //再从缓存中获取一把
            cachedResults = checkInRedisDao.getCheckInDetailsCache(scheduleId);
            if (CollectionUtils.isNotEmpty(cachedResults)) {
                log.debug("getCheckInDetailsByCache hit cache. scheduleId: {}, recordIds: {}", scheduleId, recordIds);
                return cachedResults;
            }

            List<GetCheckInDetailsResult> checkInDetails = getCheckInDetails(recordIds, scheduleId);

            if (CollectionUtils.isNotEmpty(checkInDetails)) {
                checkInRedisDao.setCheckInDetailsCache(scheduleId, checkInDetails);
                log.debug("getCheckInDetailsByCache set cache. scheduleId: {}, recordIds: {}", scheduleId, recordIds);
            }
            return checkInDetails;
        } catch (Exception e) {
            log.error("getCheckInDetailsByCache error. scheduleId: {}, recordIds: {}", scheduleId, recordIds, e);
        }
        return Collections.emptyList();
    }

    private List<GetCheckInDetailsResult> getCheckInDetails(List<Long> recordIds, Long scheduleId) {
        Result<List<GetCheckInDetailsResult>> result = checkInServiceRemote.getCheckInDetails(recordIds, scheduleId);
        if (RpcResult.isFail(result)) {
            log.warn("getCheckInDetails remote call failed. scheduleId: {}, recordIds: {}", scheduleId, recordIds);
            return Collections.emptyList();
        }

        return result.target();
    }


}